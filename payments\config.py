"""
Configuration constants for the payments module.
Centralizes hardcoded values to improve maintainability.
"""
from decimal import Decimal


class PaymentConfig:
    """Configuration constants for payments"""

    # PayPal Configuration - Sandbox
    PAYPAL_SANDBOX_CLIENT_ID = "AQVLaiTjLhAyMbzIkOv9XXf155XQbpEfCpebybwaaYFnPO8cc_cUnnNgY2KlGsa2Ta8KJTLPOmXJr3QH"
    PAYPAL_SANDBOX_CLIENT_SECRET = "EEOzergzcswVKKhA4seInDPsNUCT1w-tYh3_GAD9EHOPax9xnq0ICjJpLMG1F63GqgbnSJ33Mr15GAuM"

    # PayPal Configuration - Production (replace with actual production credentials)
    PAYPAL_PRODUCTION_CLIENT_ID = "YOUR_PRODUCTION_CLIENT_ID_HERE"
    PAYPAL_PRODUCTION_CLIENT_SECRET = "YOUR_PRODUCTION_CLIENT_SECRET_HERE"

    # PayPal URLs
    PAYPAL_SANDBOX_BASE_URL = "https://api-m.sandbox.paypal.com"
    PAYPAL_PRODUCTION_BASE_URL = "https://api-m.paypal.com"

    # Frontend URL for PayPal redirects
    FRONTEND_BASE_URL = "http://localhost:5173/local/"  # Default for development

    # Default membership fees
    DEFAULT_MEMBERSHIP_FEE = Decimal('45.00')

    # Event registration fees
    DEFAULT_EVENT_REGISTRATION_FEE = Decimal('100.00')
    LATE_REGISTRATION_FEE = Decimal('115.00')
    GROUP_REGISTRATION_FEE = Decimal('125.00')
    GUEST_FEE = Decimal('75.00')

    # Invoice settings
    INVOICE_NUMBER_FORMAT = "{year}{julian_day:03d}-{sequence:03d}"
    EVENT_INVOICE_NUMBER_FORMAT = "{year}{julian_day:03d}-E{sequence:03d}"

    # Payment processing
    PAYPAL_TIMEOUT_SECONDS = 30
    PAYPAL_MAX_RETRIES = 3
    PAYPAL_RETRY_DELAY_SECONDS = 1

    # Token cache settings (in seconds)
    PAYPAL_TOKEN_BUFFER_SECONDS = 300  # 5 minutes buffer before expiry

    # PDF generation
    DEFAULT_PDF_TEMPLATE = 'pdf/invoice.html'
    INVOICE_FILENAME_FORMAT = "invoice_{invoice_number}.pdf"
    BULK_INVOICE_FILENAME_FORMAT = "bulk_invoice_{invoice_number}.pdf"

    # Validation
    MIN_PAYMENT_AMOUNT = Decimal('0.01')
    MAX_PAYMENT_AMOUNT = Decimal('10000.00')

    # Status mappings
    PAYPAL_SUCCESS_STATUSES = ['COMPLETED', 'APPROVED']
    PAYPAL_PENDING_STATUSES = ['PENDING', 'CREATED']
    PAYPAL_FAILED_STATUSES = ['FAILED', 'CANCELLED', 'DENIED']


class ErrorMessages:
    """Standardized error messages"""

    # Payment errors
    PAYMENT_NOT_FOUND = "Payment not found"
    INVALID_PAYMENT_AMOUNT = "Payment amount must be greater than zero"
    INVALID_PAYMENT_TYPE = "Invalid payment type"
    NO_COVERED_MEMBERS = "Payment must have at least one covered member"

    # PayPal errors
    PAYPAL_CONFIG_MISSING = "PayPal configuration is missing or invalid"
    PAYPAL_TOKEN_FAILED = "Failed to obtain PayPal access token"
    PAYPAL_ORDER_FAILED = "Failed to create PayPal order"
    PAYPAL_CAPTURE_FAILED = "Failed to capture PayPal payment"
    PAYPAL_API_ERROR = "PayPal API error: {error}"

    # Member errors
    MEMBER_NOT_FOUND = "Member not found"
    PAYER_REQUIRED = "Payer is required"

    # Event errors
    EVENT_NOT_FOUND = "Event registration not found"
    EVENT_REQUIRED = "Event registration is required for event payments"

    # Invoice errors
    INVOICE_GENERATION_FAILED = "Failed to generate invoice"
    INVALID_INVOICE_DATA = "Invalid data for invoice generation"

    # General errors
    REQUIRED_FIELD = "{field} is required"
    INVALID_FORMAT = "{field} has invalid format"
    PERMISSION_DENIED = "Permission denied"


class SuccessMessages:
    """Standardized success messages"""

    PAYMENT_CREATED = "Payment created successfully"
    PAYMENT_UPDATED = "Payment updated successfully"
    PAYMENT_DELETED = "Payment deleted successfully"
    PAYMENT_CAPTURED = "Payment captured successfully"

    INVOICE_GENERATED = "Invoice generated successfully"
    INVOICE_SENT = "Invoice sent successfully"

    PAYPAL_ORDER_CREATED = "PayPal order created successfully"
    PAYPAL_PAYMENT_INITIATED = "PayPal payment initiated successfully"
