#!/usr/bin/env python
"""
Simple test script to verify the payment already paid functionality.
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mfa_backend.settings')
django.setup()

from decimal import Decimal
from django.test import RequestFactory
from django.contrib.auth.models import AnonymousUser
from rest_framework.test import force_authenticate

from core.models import Member
from payments.models import Payment
from payments.views.paypal_views import PayPalPaymentFlowView
from payments.config import ErrorMessages

def test_payment_already_paid():
    """Test that already paid payments return appropriate error message"""
    print("Testing payment already paid functionality...")
    
    # Create test member
    member = Member.objects.create(
        email='<EMAIL>',
        name='Test Member'
    )
    
    # Create test payment
    payment = Payment.objects.create(
        payer=member,
        amount=Decimal('45.00'),
        total_amount=Decimal('45.00'),
        payment_for=Payment.PaymentFor.MEMBERSHIP,
        status=Payment.PaymentStatus.SUCCESS,  # Already paid
        transaction_id='TXN_123'
    )
    payment.covered_members.add(member)
    
    # Create request
    factory = RequestFactory()
    request = factory.post('/api/payments/paypal/pay/', {
        'payment_id': payment.pk
    })
    request.user = member
    
    # Test the view
    view = PayPalPaymentFlowView()
    response = view.post(request)
    
    # Verify response
    print(f"Response status code: {response.status_code}")
    print(f"Response message: {response.data['message']}")
    print(f"Response data: {response.data['data']}")
    
    # Assertions
    assert response.status_code == 400, f"Expected 400, got {response.status_code}"
    assert response.data['message'] == ErrorMessages.PAYMENT_ALREADY_PAID, f"Expected '{ErrorMessages.PAYMENT_ALREADY_PAID}', got '{response.data['message']}'"
    assert response.data['data']['payment_id'] == payment.pk, f"Expected payment_id {payment.pk}, got {response.data['data']['payment_id']}"
    assert response.data['data']['status'] == Payment.PaymentStatus.SUCCESS, f"Expected status SUCCESS, got {response.data['data']['status']}"
    assert response.data['data']['transaction_id'] == 'TXN_123', f"Expected transaction_id TXN_123, got {response.data['data']['transaction_id']}"
    
    print("✅ Test passed! Payment already paid functionality works correctly.")
    
    # Clean up
    payment.delete()
    member.delete()

if __name__ == '__main__':
    test_payment_already_paid()
